import html2canvas from 'html2canvas';
import { jsPDF } from 'jspdf';

interface ExportToPDFOptions {
  element: HTMLElement;
  filename: string;
  title?: string;
  quality?: number;
  pageFormat?: 'a4' | 'a3' | 'letter' | 'auto';
  orientation?: 'portrait' | 'landscape';
  margin?: number;
  maxWidth?: number;
  compression?: boolean;
}

interface PDFDimensions {
  width: number;
  height: number;
  orientation: 'portrait' | 'landscape';
}

// 获取标准页面尺寸 (mm)
const getPageDimensions = (format: string): PDFDimensions => {
  const formats = {
    a4: { width: 210, height: 297 },
    a3: { width: 297, height: 420 },
    letter: { width: 216, height: 279 },
  };

  const dimensions = formats[format as keyof typeof formats] || formats.a4;
  return {
    width: dimensions.width,
    height: dimensions.height,
    orientation: 'portrait',
  };
};

// 像素转毫米的精确转换
const pxToMm = (px: number, dpi: number = 96): number => {
  return (px * 25.4) / dpi;
};

// 计算最佳图片尺寸
const calculateImageDimensions = (
  canvasWidth: number,
  canvasHeight: number,
  maxWidth: number,
  maxHeight: number,
): { width: number; height: number; scale: number } => {
  const widthRatio = maxWidth / canvasWidth;
  const heightRatio = maxHeight / canvasHeight;
  const scale = Math.min(widthRatio, heightRatio, 1);

  return {
    width: canvasWidth * scale,
    height: canvasHeight * scale,
    scale,
  };
};

// 添加中文字体支持
const setupChineseFont = (pdf: jsPDF): void => {
  try {
    // 尝试设置支持中文的字体
    pdf.setFont('helvetica', 'normal');
  } catch (error) {
    console.warn('字体设置失败，使用默认字体:', error);
  }
};

export const exportToPDF = async ({
  element,
  filename,
  title,
  quality = 2,
  pageFormat = 'a4',
  orientation = 'portrait',
  margin = 20,
  maxWidth,
  compression = true,
}: ExportToPDFOptions): Promise<{ success: boolean; error?: string }> => {
  try {
    // 验证输入参数
    if (!element) {
      throw new Error('导出元素不能为空');
    }

    if (!filename) {
      throw new Error('文件名不能为空');
    }

    // 确保文件名有正确的扩展名
    const finalFilename = filename.endsWith('.pdf') ? filename : `${filename}.pdf`;

    // 生成高质量的canvas
    const canvas = await html2canvas(element, {
      scale: Math.max(quality, 1),
      useCORS: true,
      allowTaint: true,
      logging: false,
      backgroundColor: '#ffffff',
      removeContainer: true,
      imageTimeout: 15000,
      onclone: (clonedDoc) => {
        // 确保克隆的文档样式正确
        const clonedElement = clonedDoc.querySelector('[data-html2canvas-ignore]');
        if (clonedElement) {
          clonedElement.remove();
        }
      },
    });

    if (!canvas || canvas.width === 0 || canvas.height === 0) {
      throw new Error('无法生成有效的canvas');
    }

    // 获取页面尺寸
    let pageDimensions: PDFDimensions;

    if (pageFormat === 'auto') {
      // 自动计算页面尺寸
      const canvasWidthMm = pxToMm(canvas.width);
      const canvasHeightMm = pxToMm(canvas.height);

      pageDimensions = {
        width: canvasWidthMm + margin * 2,
        height: canvasHeightMm + margin * 2 + (title ? 30 : 0),
        orientation: canvasWidthMm > canvasHeightMm ? 'landscape' : 'portrait',
      };
    } else {
      pageDimensions = getPageDimensions(pageFormat);
      if (orientation === 'landscape') {
        [pageDimensions.width, pageDimensions.height] = [pageDimensions.height, pageDimensions.width];
        pageDimensions.orientation = 'landscape';
      }
    }

    // 计算可用空间
    const titleHeight = title ? 25 : 0;
    const availableWidth = pageDimensions.width - margin * 2;
    const availableHeight = pageDimensions.height - margin * 2 - titleHeight;

    // 使用maxWidth限制或可用宽度
    const effectiveMaxWidth = maxWidth ? Math.min(maxWidth, availableWidth) : availableWidth;

    // 计算图片尺寸
    const canvasWidthMm = pxToMm(canvas.width);
    const canvasHeightMm = pxToMm(canvas.height);

    const imageDimensions = calculateImageDimensions(canvasWidthMm, canvasHeightMm, effectiveMaxWidth, availableHeight);

    // 创建PDF
    const pdf = new jsPDF({
      orientation: pageDimensions.orientation,
      unit: 'mm',
      format: [pageDimensions.width, pageDimensions.height],
      compress: compression,
    });

    // 设置中文字体支持
    setupChineseFont(pdf);

    // 添加标题
    if (title) {
      pdf.setFontSize(16);
      pdf.setTextColor(0, 0, 0);

      // 计算标题位置（居中）
      const titleWidth = pdf.getTextWidth(title);
      const titleX = (pageDimensions.width - titleWidth) / 2;

      pdf.text(title, titleX, margin + 15);

      // 添加分隔线
      pdf.setDrawColor(200, 200, 200);
      pdf.setLineWidth(0.5);
      pdf.line(margin, margin + 20, pageDimensions.width - margin, margin + 20);
    }

    // 转换canvas为图片数据
    const imageFormat = compression ? 'JPEG' : 'PNG';
    const imageQuality = compression ? 0.85 : 1.0;
    const imgData = canvas.toDataURL(`image/${imageFormat.toLowerCase()}`, imageQuality);

    // 计算图片位置（居中）
    const imageX = (pageDimensions.width - imageDimensions.width) / 2;
    const imageY = margin + titleHeight;

    // 添加图片到PDF
    pdf.addImage(
      imgData,
      imageFormat,
      imageX,
      imageY,
      imageDimensions.width,
      imageDimensions.height,
      undefined,
      compression ? 'MEDIUM' : 'NONE',
    );

    // 保存PDF
    pdf.save(finalFilename);

    return { success: true };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '未知错误';
    console.error('PDF导出失败:', errorMessage, error);

    return {
      success: false,
      error: `PDF导出失败: ${errorMessage}`,
    };
  }
};
