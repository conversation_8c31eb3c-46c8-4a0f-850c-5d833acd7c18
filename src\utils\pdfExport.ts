import html2canvas from 'html2canvas';
import { jsPDF } from 'jspdf';

interface ExportToPDFOptions {
  element: HTMLElement;
  filename: string;
  title?: string;
  quality?: number;
  pageFormat?: 'a4' | 'a3' | 'letter' | 'auto';
  orientation?: 'portrait' | 'landscape';
  margin?: number;
  maxWidth?: number;
  compression?: boolean;
}

interface PDFDimensions {
  width: number;
  height: number;
  orientation: 'portrait' | 'landscape';
}

// 获取标准页面尺寸 (mm)
const getPageDimensions = (format: string): PDFDimensions => {
  const formats = {
    a4: { width: 210, height: 297 },
    a3: { width: 297, height: 420 },
    letter: { width: 216, height: 279 },
  };

  const dimensions = formats[format as keyof typeof formats] || formats.a4;
  return {
    width: dimensions.width,
    height: dimensions.height,
    orientation: 'portrait',
  };
};

// 像素转毫米的精确转换
const pxToMm = (px: number, dpi: number = 96): number => {
  return (px * 25.4) / dpi;
};

// 计算最佳图片尺寸
const calculateImageDimensions = (
  canvasWidth: number,
  canvasHeight: number,
  maxWidth: number,
  maxHeight: number,
): { width: number; height: number; scale: number } => {
  const widthRatio = maxWidth / canvasWidth;
  const heightRatio = maxHeight / canvasHeight;
  const scale = Math.min(widthRatio, heightRatio, 1);

  return {
    width: canvasWidth * scale,
    height: canvasHeight * scale,
    scale,
  };
};

// 添加中文字体支持
const setupChineseFont = (pdf: jsPDF): void => {
  try {
    // 尝试设置支持中文的字体
    pdf.setFont('helvetica', 'normal');
  } catch (error) {
    console.warn('字体设置失败，使用默认字体:', error);
  }
};

export const exportToPDF = async ({
  element,
  filename,
  title,
  quality = 2,
  pageFormat = 'a4',
  orientation = 'portrait',
  margin = 20,
  maxWidth,
  compression = true,
}: ExportToPDFOptions): Promise<{ success: boolean; error?: string }> => {
  try {
    // 验证输入参数
    if (!element) {
      throw new Error('导出元素不能为空');
    }

    if (!filename) {
      throw new Error('文件名不能为空');
    }

    // 确保文件名有正确的扩展名
    const finalFilename = filename.endsWith('.pdf') ? filename : `${filename}.pdf`;

    // 确保元素完全可见并获取完整高度
    const originalStyle = {
      height: element.style.height,
      maxHeight: element.style.maxHeight,
      overflow: element.style.overflow,
    };

    // 临时设置样式以确保完整内容可见
    element.style.height = 'auto';
    element.style.maxHeight = 'none';
    element.style.overflow = 'visible';

    // 等待一下让布局稳定
    await new Promise((resolve) => setTimeout(resolve, 100));

    // 生成高质量的canvas
    const canvas = await html2canvas(element, {
      scale: Math.max(quality, 1),
      useCORS: true,
      allowTaint: true,
      logging: false,
      backgroundColor: '#ffffff',
      removeContainer: true,
      imageTimeout: 15000,
      height: element.scrollHeight, // 使用scrollHeight确保获取完整高度
      windowHeight: element.scrollHeight,
      onclone: (clonedDoc) => {
        // 确保克隆的文档样式正确
        const clonedElement = clonedDoc.querySelector('[data-html2canvas-ignore]');
        if (clonedElement) {
          clonedElement.remove();
        }

        // 在克隆文档中也设置相同的样式
        const clonedTarget = clonedDoc.querySelector('.content');
        if (clonedTarget) {
          (clonedTarget as HTMLElement).style.height = 'auto';
          (clonedTarget as HTMLElement).style.maxHeight = 'none';
          (clonedTarget as HTMLElement).style.overflow = 'visible';
        }
      },
    });

    // 恢复原始样式
    element.style.height = originalStyle.height;
    element.style.maxHeight = originalStyle.maxHeight;
    element.style.overflow = originalStyle.overflow;

    if (!canvas || canvas.width === 0 || canvas.height === 0) {
      throw new Error('无法生成有效的canvas');
    }

    // 获取页面尺寸
    let pageDimensions: PDFDimensions;

    if (pageFormat === 'auto') {
      // 自动计算页面尺寸，但限制最大高度避免过长
      const canvasWidthMm = pxToMm(canvas.width);
      const canvasHeightMm = pxToMm(canvas.height);
      const maxAutoHeight = 400; // 限制自动模式的最大高度

      pageDimensions = {
        width: canvasWidthMm + margin * 2,
        height: Math.min(canvasHeightMm + margin * 2 + (title ? 30 : 0), maxAutoHeight),
        orientation: canvasWidthMm > canvasHeightMm ? 'landscape' : 'portrait',
      };
    } else {
      pageDimensions = getPageDimensions(pageFormat);
      if (orientation === 'landscape') {
        [pageDimensions.width, pageDimensions.height] = [pageDimensions.height, pageDimensions.width];
        pageDimensions.orientation = 'landscape';
      }
    }

    // 计算可用空间
    const titleHeight = title ? 25 : 0;
    const availableWidth = pageDimensions.width - margin * 2;
    const availableHeight = pageDimensions.height - margin * 2 - titleHeight;

    // 使用maxWidth限制或可用宽度
    const effectiveMaxWidth = maxWidth ? Math.min(maxWidth, availableWidth) : availableWidth;

    // 计算图片尺寸
    const canvasWidthMm = pxToMm(canvas.width);
    const canvasHeightMm = pxToMm(canvas.height);

    // 创建PDF
    const pdf = new jsPDF({
      orientation: pageDimensions.orientation,
      unit: 'mm',
      format: [pageDimensions.width, pageDimensions.height],
      compress: compression,
    });

    // 设置中文字体支持
    setupChineseFont(pdf);

    // 转换canvas为图片数据
    const imageFormat = compression ? 'JPEG' : 'PNG';
    const imageQuality = compression ? 0.85 : 1.0;
    const imgData = canvas.toDataURL(`image/${imageFormat.toLowerCase()}`, imageQuality);

    // 计算图片尺寸 - 优先保持完整性，如果太高则使用auto模式
    let finalImageDimensions;
    let finalPageDimensions = pageDimensions;

    if (canvasHeightMm > availableHeight && pageFormat !== 'auto') {
      // 内容太高，切换到auto模式以保持完整性
      finalPageDimensions = {
        width: Math.max(canvasWidthMm + margin * 2, pageDimensions.width),
        height: canvasHeightMm + margin * 2 + titleHeight,
        orientation: canvasWidthMm > canvasHeightMm ? 'landscape' : 'portrait',
      };

      // 重新创建PDF以使用新的页面尺寸
      const newPdf = new jsPDF({
        orientation: finalPageDimensions.orientation,
        unit: 'mm',
        format: [finalPageDimensions.width, finalPageDimensions.height],
        compress: compression,
      });

      // 替换原PDF对象
      Object.setPrototypeOf(pdf, Object.getPrototypeOf(newPdf));
      Object.assign(pdf, newPdf);

      // 重新设置字体
      setupChineseFont(pdf);

      // 重新计算可用空间
      const newAvailableWidth = finalPageDimensions.width - margin * 2;
      const newAvailableHeight = finalPageDimensions.height - margin * 2 - titleHeight;
      const newEffectiveMaxWidth = maxWidth ? Math.min(maxWidth, newAvailableWidth) : newAvailableWidth;

      finalImageDimensions = calculateImageDimensions(canvasWidthMm, canvasHeightMm, newEffectiveMaxWidth, newAvailableHeight);
    } else {
      // 使用原始计算
      finalImageDimensions = calculateImageDimensions(canvasWidthMm, canvasHeightMm, effectiveMaxWidth, availableHeight);
    }

    // 添加标题
    if (title) {
      pdf.setFontSize(16);
      pdf.setTextColor(0, 0, 0);

      const titleWidth = pdf.getTextWidth(title);
      const titleX = (finalPageDimensions.width - titleWidth) / 2;

      pdf.text(title, titleX, margin + 15);

      // 添加分隔线
      pdf.setDrawColor(200, 200, 200);
      pdf.setLineWidth(0.5);
      pdf.line(margin, margin + 20, finalPageDimensions.width - margin, margin + 20);
    }

    // 计算图片位置（居中）
    const imageX = (finalPageDimensions.width - finalImageDimensions.width) / 2;
    const imageY = margin + titleHeight;

    // 添加图片到PDF
    pdf.addImage(
      imgData,
      imageFormat,
      imageX,
      imageY,
      finalImageDimensions.width,
      finalImageDimensions.height,
      undefined,
      compression ? 'MEDIUM' : 'NONE',
    );

    // 保存PDF
    pdf.save(finalFilename);

    return { success: true };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '未知错误';
    console.error('PDF导出失败:', errorMessage, error);

    return {
      success: false,
      error: `PDF导出失败: ${errorMessage}`,
    };
  }
};
