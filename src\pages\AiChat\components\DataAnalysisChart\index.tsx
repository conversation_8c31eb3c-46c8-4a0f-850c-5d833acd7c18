'use client';

import type React from 'react';
import { useState, useEffect, useRef, useMemo } from 'react';
import * as echarts from 'echarts';
import { Button, Tabs, Dropdown, Space, Card, Typography, message, Table, Modal, Select, Input } from 'antd';
import {
  DownloadOutlined,
  EllipsisOutlined,
  UploadOutlined,
  TableOutlined,
  PictureOutlined,
  SettingOutlined,
  FullscreenOutlined,
  EyeOutlined,
  MessageOutlined,
  SaveOutlined,
} from '@ant-design/icons';
import ExportImageModal from './components/ExportImageModal';
import ExportTableModal from './components/ExportTableModal';
import ParameterSettingsModal from '@/components/ParameterSettingsModal';
import FilePreview from './components/FilePreview';
import FullScreenChartModal from '@/pages/Dashboard/components/FullScreenChartModal';
import './index.less';
import { extractTableFromOption } from '@/utils/echartsToTable';
import { downloadFile, getMysqlTableFields } from '@/services/DataLoom/fileController';
import { useModel } from '@umijs/max';
import { transferFile } from '@/services/DataLoom/aiController';
import { addAiChart, listAllDashboard, updateChartStyle, downloadChartData } from '@/services/DataLoom/yibiaopanjiekou';
import ReactMarkdown from 'react-markdown'; // React 渲染 Markdown 的核心库
import remarkGfm from 'remark-gfm'; // 支持 GitHub 风格的 Markdown（如表格、任务列表等）
import remarkBreaks from 'remark-breaks'; // 把换行符\n渲染成 <br>
import rehypeRaw from 'rehype-raw'; // 允许 Markdown 里混用 HTML
import FilePreviewModal from './components/FilePreviewModal';
import * as XLSX from 'xlsx';
import Papa from 'papaparse';
import { ChatMessage } from '@/models/chatHistory';
import { exportToPDF } from '@/utils/pdfExport';

const { TabPane } = Tabs;
const { Title, Paragraph, Text } = Typography;

interface DataAnalysisProps {
  onBack: () => void;
  analyst: {
    id: string;
    type: string;
    assistantName: string;
    description: string;
    icon: string;
  } | null;
  activeTab?: string;
  onTabChange?: (key: string) => void;
}

const DataAnalysis: React.FC<DataAnalysisProps> = ({ onBack, analyst, activeTab: propActiveTab, onTabChange }) => {
  const [internalActiveTab, setInternalActiveTab] = useState<string>('analysisResult');
  const [exportImageVisible, setExportImageVisible] = useState(false);
  const [exportTableVisible, setExportTableVisible] = useState(false);
  const [settingsVisible, setSettingsVisible] = useState(false);
  const [chartInstances, setChartInstances] = useState<{ [key: string]: echarts.ECharts }>({});
  const [fullScreenVisible, setFullScreenVisible] = useState(false);
  const [showTable, setShowTable] = useState<{ [key: string]: boolean }>({});
  const [tableData, setTableData] = useState<{ [key: string]: { columns: any[]; dataSource: any[] } }>({});
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  const { selectedSources, addSource, removeSource } = useModel('dataSource');
  const { chatHistory, updateMessage } = useModel('chatHistory');
  const [previewVisible, setPreviewVisible] = useState(false);
  const [luckysheetData, setLuckysheetData] = useState<any[]>([]);
  const [messageApi, contextHolder] = message.useMessage();
  const [selectedMessageIndex, setSelectedMessageIndex] = useState<number>(0);
  const [exportPdfLoading, setExportPdfLoading] = useState(false);
  const lastChatHistoryRef = useRef<string>('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const isFirstLoad = useRef(true);
  const [dashboardSelectVisible, setDashboardSelectVisible] = useState(false);
  const [dashboards, setDashboards] = useState<any[]>([]);
  const [selectedDashboard, setSelectedDashboard] = useState<string>('');
  const [selectedMessageId, setSelectedMessageId] = useState<string>('');
  const [chartName, setChartName] = useState<string>('');

  // 使用 useMemo 优化助手消息的过滤
  const assistantMessages = useMemo(() => {
    return chatHistory?.filter((msg) => msg.role === 'assistant') || [];
  }, [chatHistory]);
  // 分析结果标题
  const analysisResultTitle = useMemo<string>(() => {
    // 查找第一条用户消息
    return chatHistory?.find((msg) => msg.role === 'user')?.content || '';
  }, [chatHistory]);

  // 添加一个 useMemo 来处理去重后的文件列表
  const uniqueFiles = useMemo(() => {
    const fileSet = new Set<string>();
    const files: { file: string; messageIndex: number }[] = [];

    assistantMessages.forEach((message, messageIndex) => {
      if (message.uploaded_files?.length) {
        message.uploaded_files.forEach((file) => {
          if (!fileSet.has(file)) {
            fileSet.add(file);
            files.push({ file, messageIndex });
          }
        });
      }
    });

    return files;
  }, [assistantMessages]);

  // 封装图表渲染逻辑
  const renderCharts = (messages: ChatMessage[]) => {
    const charts: echarts.ECharts[] = [];
    const resizeHandlers: (() => void)[] = [];
    const newChartInstances: { [key: string]: echarts.ECharts } = {};

    messages.forEach((message, index) => {
      try {
        const option = message.echart_option?.[0]?.option;
        if (!option || showTable[message.id || '']) return;

        const chartDom = document.getElementById(`chart-container-${index}`);
        if (!chartDom) return;

        // 🛡️ 防止重复 init 报错：echarts 会提示 DOM 已初始化
        let chartInstance = echarts.getInstanceByDom(chartDom);
        if (!chartInstance) {
          chartInstance = echarts.init(chartDom);
        }

        // 确保 option 是有效的对象
        if (typeof option === 'object' && option !== null) {
          // 验证并清理配置项
          const cleanOption = {
            ...option,
            // 确保必要的配置项存在
            title: {
              text: '',
            },
            grid: {
              ...(option.grid || {
                grid: {
                  containLabel: true,
                },
              }),
            },
            // 确保 series 是数组
            series: Array.isArray(option.series) ? option.series : [option.series].filter(Boolean),
          };

          // 为每个图表设置独立的配置
          chartInstance.setOption(cleanOption, true); // 添加 true 参数以清除之前的配置
          charts.push(chartInstance);
          newChartInstances[message.id || ''] = chartInstance;

          const handleResize = () => {
            if (chartInstance && !chartInstance.isDisposed()) {
              chartInstance.resize();
            }
          };
          window.addEventListener('resize', handleResize);
          resizeHandlers.push(handleResize);
        }
      } catch (error) {
        // 如果图表初始化失败，尝试清理相关资源
        const chartDom = document.getElementById(`chart-container-${index}`);
        if (chartDom) {
          const existingChart = echarts.getInstanceByDom(chartDom);
          if (existingChart) {
            existingChart.dispose();
          }
        }
      }
    });

    // 一次性更新所有图表实例
    setChartInstances(newChartInstances);

    return {
      charts,
      resizeHandlers,
    };
  };

  // 清理图表资源
  const cleanupCharts = (charts: echarts.ECharts[], resizeHandlers: (() => void)[]) => {
    charts.forEach((chart) => {
      if (chart && !chart.isDisposed()) {
        chart.dispose();
      }
    });
    resizeHandlers.forEach((handler) => {
      window.removeEventListener('resize', handler);
    });
  };

  useEffect(() => {
    if (propActiveTab !== 'analysisResult') return;

    // 检查 assistantMessages 是否真的发生了变化
    const currentChatHistory = JSON.stringify(assistantMessages);
    if (currentChatHistory === lastChatHistoryRef.current) {
      return;
    }
    lastChatHistoryRef.current = currentChatHistory;

    const { charts, resizeHandlers } = renderCharts(assistantMessages);

    // 只在非第一次加载且 assistantMessages 变化时滚动到底部
    if (!isFirstLoad.current) {
      scrollToBottom();
    }
    isFirstLoad.current = false;

    return () => cleanupCharts(charts, resizeHandlers);
  }, [propActiveTab, showTable, assistantMessages]);

  // 处理 tab 切换时的图表重新渲染
  useEffect(() => {
    if (propActiveTab === 'analysisResult') {
      const { charts, resizeHandlers } = renderCharts(assistantMessages);
      return () => cleanupCharts(charts, resizeHandlers);
    }
  }, [propActiveTab]);

  const scrollToBottom = () => {
    setTimeout(() => {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, 100);
  };

  const handleExportTableOk = (format: string) => {
    const params = {
      fileName: assistantMessages.find((msg) => msg.id === selectedMessageId)?.echart_option[0].file_name, // 文件名
      id: selectedMessageId, // 图表id
      fields: assistantMessages.find((msg) => msg.id === selectedMessageId)?.echart_option[0].fields, // 字段
      downloadType: format, // 下载类型
    };
    downloadChartData(params, {
      responseType: 'blob',
      getResponse: true,
    })
      .then((response) => {
        if (response) {
          // 从响应头中获取文件名
          const disposition = response.headers?.['content-disposition'];
          let filename = '';
          if (disposition) {
            const filenameMatch = disposition.match(/filename="(.+)"/);
            if (filenameMatch && filenameMatch[1]) {
              filename = filenameMatch[1];
            }
          }

          // 如果没有从响应头获取到文件名，则使用默认文件名
          if (!filename) {
            const fileExtension = format === 'csv' ? '.csv' : '.xlsx';
            filename = `${params.fileName || 'exported_data'}${fileExtension}`;
          }

          // 对CSV文件进行特殊处理
          if (format === 'csv') {
            // 将Blob转换为文本
            response.data.text().then((text: string) => {
              // 添加UTF-8 BOM标记
              const bom = '\uFEFF';
              const withBom = bom + text;
              // 创建新的Blob，指定UTF-8编码
              const blob = new Blob([withBom], { type: 'text/csv;charset=utf-8' });
              const url = window.URL.createObjectURL(blob);
              const link = document.createElement('a');
              link.href = url;
              link.download = filename;
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
              window.URL.revokeObjectURL(url);
            });
          } else {
            // 其他格式文件按原方式处理
            const url = window.URL.createObjectURL(response.data);
            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
          }

          message.success('导出表格成功');
          setExportTableVisible(false);
          setSelectedMessageId('');
        }
      })
      .catch((error) => {
        console.error('导出表格失败:', error);
        message.error('导出表格失败');
      });
  };

  const handleSettingsOk = (settings: any, chartOption: any) => {
    setSettingsVisible(false);
    if (selectedMessageId) {
      const currentMessage = assistantMessages.find((msg) => msg.id === selectedMessageId);
      if (currentMessage?.echart_option?.[0]) {
        updateMessage(selectedMessageId, {
          echart_option: [
            {
              ...currentMessage.echart_option[0],
              option: chartOption,
            },
          ],
        });
        // 只替换option
        const newChartOption = {
          ...currentMessage.echart_option[0],
          option: chartOption,
        };
        const params = {
          id: selectedMessageId,
          chartStyle: JSON.stringify(settings),
          chartOption: JSON.stringify([newChartOption]),
        };
        console.log(params, 'params');
        updateChartStyle(params).then((res) => {
          if (res.code === 0) {
            message.success('图表样式修改成功');
          } else {
            message.error('图表样式修改失败');
          }
        });
      }
      // 禁止滚动
      isFirstLoad.current = true;
    }
  };

  const handleToggleView = (message: ChatMessage, index: number) => {
    const messageId = message.id || '';
    if (!showTable[messageId]) {
      const { columns, dataSource } = extractTableFromOption(message.echart_option[0].option);
      setTableData((prev) => ({
        ...prev,
        [messageId]: { columns, dataSource },
      }));
    }
    setShowTable((prev) => ({
      ...prev,
      [messageId]: !prev[messageId],
    }));
    // 如果切换到图表视图，需要重新初始化图表
    if (showTable[messageId]) {
      setTimeout(() => {
        const chartDom = document.getElementById(`chart-container-${index}`);
        if (!chartDom) return;

        let chartInstance = echarts.getInstanceByDom(chartDom);
        if (!chartInstance) {
          chartInstance = echarts.init(chartDom);
        }
        chartInstance.setOption(message.echart_option[0].option);
        setChartInstances((prev) => ({
          ...prev,
          [messageId]: chartInstance,
        }));
      }, 0);
    }
  };
  const handleUploadDashboard = (message: ChatMessage, index: number) => {
    setSelectedMessageId(message.id || '');
    setDashboardSelectVisible(true);
    setChartName(`AI分析图表_${new Date().toLocaleString()}`);
    loadDashboards();
  };

  const loadDashboards = async () => {
    try {
      const res = await listAllDashboard();
      if (res.code === 0 && res.data) {
        const dashboardOptions = res.data.map((item: any) => ({
          label: item.name,
          value: item.id.toString(),
        }));
        setDashboards(dashboardOptions);
        // 默认选择第一个仪表盘
        setSelectedDashboard(dashboardOptions[0].value);
      } else {
        message.error('获取仪表盘列表失败');
      }
    } catch (error) {
      message.error('获取仪表盘列表失败');
    }
  };

  const handleDashboardSelect = async () => {
    if (!selectedDashboard) {
      message.warning('请选择仪表盘');
      return;
    }

    if (!chartName.trim()) {
      message.warning('请输入图表名称');
      return;
    }

    try {
      const currentMessage = assistantMessages.find((msg) => msg.id === selectedMessageId);
      if (!currentMessage?.echart_option?.[0]) {
        message.error('图表数据不存在');
        return;
      }

      const res = await addAiChart({
        dashboardId: selectedDashboard,
        chartName: chartName.trim(),
        chartOption: JSON.stringify(currentMessage.echart_option[0].option),
        chartType: currentMessage.echart_option[0].type,
        analysisRes: currentMessage.echart_option[0].conclusion,
      });

      if (res.code === 0) {
        message.success('添加图表成功');
        setDashboardSelectVisible(false);
        setSelectedDashboard('');
        setChartName('');
        setSelectedMessageId('');
      } else {
        message.error(res.message || '添加图表失败');
      }
    } catch (error) {
      message.error('添加图表失败');
    }
  };

  const handleDownloadFile = async (file: string) => {
    if (!currentUser?.id) {
      message.error('用户信息获取失败');
      return;
    }

    try {
      const response = await downloadFile({
        fileName: file,
        userId: currentUser.id.toString(),
      });

      const fileExtension = file.split('.').pop()?.toLowerCase();
      let blob: Blob;

      if (['csv', 'txt'].includes(fileExtension || '')) {
        // 先解码为字符串
        const buffer = await response.arrayBuffer();
        const text = new TextDecoder().decode(buffer);
        // 添加 BOM
        const bom = '\uFEFF';
        const withBom = bom + text;
        // 再编码为 Uint8Array
        const uint8Array = new TextEncoder().encode(withBom);
        blob = new Blob([uint8Array], { type: 'text/plain;charset=utf-8' });
      } else {
        // 其他文件类型按原方式处理
        blob = new Blob([response], { type: 'application/octet-stream' });
      }

      const url = window.URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = url;
      link.download = file;

      document.body.appendChild(link);
      link.click();

      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      message.success('文件下载成功');
    } catch (error) {
      message.error('文件下载失败');
      console.error('下载文件失败:', error);
    }
  };

  const fetchCSVData = async (source: any) => {
    if (!currentUser?.id) {
      message.error('用户信息不存在');
      return;
    }

    try {
      const response = await downloadFile({
        fileName: source,
        userId: currentUser.id.toString(),
      });

      const buffer = await response.arrayBuffer();
      const fileExtension = source.split('.').pop()?.toLowerCase();
      let raw: string[][] = [];

      if (fileExtension === 'csv') {
        const text = new TextDecoder('utf-8').decode(buffer);
        if (!text || text.trim().length === 0) {
          throw new Error('文件内容为空');
        }

        // 移除UTF-8 BOM标记
        const cleanText = text.charCodeAt(0) === 0xfeff ? text.slice(1) : text;

        const result = Papa.parse<string[]>(cleanText, {
          header: false,
          skipEmptyLines: true,
          delimiter: ',', // 明确指定分隔符为英文逗号
          transform: (value: string) => value.trim(),
        });

        if (result.errors.length > 0) {
          throw new Error('CSV文件格式错误');
        }

        raw = result.data as string[][];
      } else if (fileExtension === 'xls' || fileExtension === 'xlsx') {
        const workbook = XLSX.read(buffer, { type: 'array' });
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];
        raw = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as string[][];

        if (!raw || raw.length === 0) {
          throw new Error('Excel文件内容为空');
        }
      } else {
        throw new Error('不支持的文件格式');
      }
      setPreviewVisible(true);
      setLuckysheetData(raw);
    } catch (err) {
      message.error(`文件 ${source} 加载失败: ${err instanceof Error ? err.message : '未知错误'}`);
    }
  };
  const handleSaveFile = (file: string) => {
    if (!currentUser?.id) {
      message.error('用户信息获取失败');
      return;
    }
    transferFile({
      fileName: file,
      userId: currentUser?.id?.toString(),
    }).then((res) => {
      if (res.code === 0) {
        message.success('文件转存成功');
      } else {
        message.error('文件转存失败');
      }
    });
  };

  // 下拉菜单项（需要改为函数，接收 index）
  const getDropdownItems = (index: number) => [
    {
      key: '1',
      label: '导出图片',
      icon: <img src="/assets/h5t8mk0n.svg" alt="" />,
      onClick: () => {
        setSelectedMessageId(assistantMessages[index].id || '');
        setExportImageVisible(true);
      },
    },
    {
      key: '2',
      label: '导出表格',
      icon: <img src="/assets/krvbp7p6.svg" alt="" />,
      onClick: () => {
        setSelectedMessageId(assistantMessages[index].id || '');
        setExportTableVisible(true);
      },
    },
    {
      key: '3',
      label: '参数设置',
      icon: <img src="/assets/ddskcuq5.svg" alt="" />,
      onClick: () => {
        setSelectedMessageId(assistantMessages[index].id || '');
        setSettingsVisible(true);
      },
    },
  ];

  const handleExportPDF = async () => {
    if (!assistantMessages.length) {
      messageApi.warning('没有可导出的内容');
      return;
    }

    setExportPdfLoading(true);
    try {
      // 尝试多个选择器来找到完整的内容区域
      let contentElement = document.querySelector('.content') as HTMLElement;

      if (!contentElement) {
        // 如果找不到.content，尝试其他选择器
        contentElement =
          (document.querySelector('.analysis-content') as HTMLElement) ||
          (document.querySelector('[class*="content"]') as HTMLElement) ||
          (document.querySelector('.ant-tabs-content') as HTMLElement);
      }

      if (!contentElement) {
        throw new Error('未找到导出内容区域');
      }

      // 确保内容完全展开
      console.log('导出元素信息:', {
        scrollHeight: contentElement.scrollHeight,
        offsetHeight: contentElement.offsetHeight,
        clientHeight: contentElement.clientHeight,
        className: contentElement.className,
      });

      const result = await exportToPDF({
        element: contentElement,
        filename: `${analyst?.assistantName || '分析报告'}_${new Date().toLocaleDateString().replace(/\//g, '-')}`,
        title: analyst?.assistantName || '数据分析报告',
        quality: 2,
        pageFormat: 'a4',
        orientation: 'portrait',
        margin: 15,
        compression: true,
      });

      if (result.success) {
        messageApi.success('PDF导出成功');
      } else {
        messageApi.error(result.error || 'PDF导出失败');
      }
    } catch (error) {
      console.error('PDF导出错误:', error);
      messageApi.error('PDF导出失败');
    } finally {
      setExportPdfLoading(false);
    }
  };

  return (
    <div className="data-analysis-container">
      {contextHolder}
      <div className="header-wrapper">
        <Tabs
          defaultActiveKey="analysisResult"
          activeKey={propActiveTab || internalActiveTab}
          onChange={(key) => {
            setInternalActiveTab(key);
            onTabChange?.(key);
          }}
          tabBarExtraContent={{
            right: selectedSources?.length > 0 ? <Text className="shared-apps">共{selectedSources?.length}个页签</Text> : null,
          }}
          className="custom-tabs"
          items={[
            ...(selectedSources?.length > 0 ? [{ key: 'filePreview', label: '文件预览' }] : []),
            ...(assistantMessages.length > 0 &&
            (assistantMessages.some((msg) => msg.echart_option?.[0] && Object.keys(msg.echart_option[0]).length > 0) ||
              uniqueFiles?.length > 0)
              ? [{ key: 'analysisResult', label: '分析结果' }]
              : []),
          ]}
        />
      </div>

      <div className="content">
        {propActiveTab === 'filePreview' ? (
          <FilePreview onOk={() => {}} onCancel={() => {}} />
        ) : (
          <>
            <div className="title-row">
              <Title level={4} className="analysis-title" title={analysisResultTitle}>
                {analysisResultTitle}
              </Title>
              {assistantMessages.some((msg) => msg.echart_option?.[0] && Object.keys(msg.echart_option[0]).length > 0) && (
                <Button type="primary" className="button-right" onClick={handleExportPDF} loading={exportPdfLoading}>
                  下载分析报告
                </Button>
              )}
            </div>
            <div className="analysis-card-container">
              {assistantMessages.length > 0 &&
                assistantMessages.some((msg) => msg.echart_option?.[0] && Object.keys(msg.echart_option[0]).length > 0) && (
                  <div className="analysis-card-wrapper">
                    {/* 标题 */}
                    <div className="analysis-card-title">
                      <img src="/assets/e7kpvcfd.svg" alt="" />
                      <span>数据看板</span>
                    </div>
                    {assistantMessages.map(
                      (message, index) =>
                        message.echart_option?.[0] &&
                        Object.keys(message.echart_option[0]).length > 0 && (
                          <div key={index} className="analysis-card-wrapper">
                            <div className="action-bar">
                              {/* 图表名称 */}
                              <div className="chart-name">{message.echart_option[0].option?.title?.text}</div>
                              {/* 操作按钮 */}
                              <Space className="right-actions" size={10}>
                                <Button color="primary" variant="outlined" size="small" onClick={() => handleToggleView(message, index)}>
                                  {showTable[message.id || ''] ? '显示图表' : '显示数据'}
                                </Button>
                                <Button type="primary" size="small" onClick={() => handleUploadDashboard(message, index)}>
                                  上传仪表盘
                                </Button>
                                <Dropdown menu={{ items: getDropdownItems(index) }} placement="bottomRight" trigger={['click']}>
                                  <img src="/assets/uz3ou90h.svg" alt="" style={{ cursor: 'pointer', width: 20, height: 20 }} />
                                </Dropdown>
                              </Space>
                            </div>
                            <Card className="chart-card" style={{ border: 'none' }}>
                              {showTable[message.id || ''] ? (
                                <Table
                                  dataSource={tableData[message.id || '']?.dataSource}
                                  columns={tableData[message.id || '']?.columns}
                                  pagination={false}
                                  size="small"
                                  scroll={{ y: '400px' }}
                                />
                              ) : (
                                <div id={`chart-container-${index}`} className="chart-container"></div>
                              )}
                            </Card>

                            <div className="view-full">
                              <Button
                                type="text"
                                onClick={() => {
                                  setSelectedMessageId(message.id || '');
                                  setFullScreenVisible(true);
                                }}
                              >
                                <span style={{ fontSize: 12, color: '#2868e7' }}>查看大图</span>
                              </Button>
                            </div>
                            <Paragraph className="analysis-content paragraph-body">
                              <ReactMarkdown remarkPlugins={[remarkGfm, remarkBreaks]} rehypePlugins={[rehypeRaw]}>
                                {message.echart_option?.[0]?.conclusion}
                              </ReactMarkdown>
                            </Paragraph>
                          </div>
                        ),
                    )}
                  </div>
                )}
              <div ref={messagesEndRef} />
              {uniqueFiles.length > 0 && (
                <Card className="file-bar-wrapper">
                  <Title level={4} className="analysis-title">
                    文件列表
                  </Title>
                  {uniqueFiles.map(({ file, messageIndex }, fileIndex) => (
                    <div key={`${messageIndex}-${fileIndex}`} className="file-bar">
                      <span className="file-name">{file || '未命名文件'}</span>
                      <div className="file-action-bar">
                        <div
                          className="file-action-btn"
                          onClick={() => {
                            fetchCSVData(file);
                          }}
                        >
                          <EyeOutlined className="icon" />
                          预览
                        </div>
                        <div className="divider" />
                        <div className="file-action-btn" onClick={() => handleDownloadFile(file)}>
                          <DownloadOutlined className="icon" />
                          下载
                        </div>
                        <div className="divider" />
                        <div className="file-action-btn" onClick={() => handleSaveFile(file)}>
                          <SaveOutlined className="icon" />
                          转存
                        </div>
                      </div>
                    </div>
                  ))}
                </Card>
              )}
            </div>
          </>
        )}
      </div>

      <ExportImageModal
        visible={exportImageVisible}
        onCancel={() => setExportImageVisible(false)}
        chartInstance={chartInstances[selectedMessageId || '']}
      />
      <ExportTableModal visible={exportTableVisible} onCancel={() => setExportTableVisible(false)} onOk={handleExportTableOk} data={[]} />
      <ParameterSettingsModal
        visible={settingsVisible}
        chartStyle={assistantMessages.find((msg) => msg.id === selectedMessageId)?.chartStyle}
        chartOption={assistantMessages.find((msg) => msg.id === selectedMessageId)?.echart_option[0].option}
        onCancel={() => setSettingsVisible(false)}
        onOk={handleSettingsOk}
        chartType={assistantMessages.find((msg) => msg.id === selectedMessageId)?.echart_option[0].type}
      />
      <FullScreenChartModal
        visible={fullScreenVisible}
        onClose={() => setFullScreenVisible(false)}
        title={analyst?.assistantName || ''}
        width="80%"
        height="85vh"
        chartOption={assistantMessages.find((msg) => msg.id === selectedMessageId)?.echart_option[0].option}
        children={<></>}
        showSettings={false}
      />
      <FilePreviewModal visible={previewVisible} onCancel={() => setPreviewVisible(false)} dataSource={luckysheetData} />
      <Modal
        className="dashboard-select-modal common-modal"
        title="选择仪表盘"
        open={dashboardSelectVisible}
        onOk={handleDashboardSelect}
        onCancel={() => {
          setDashboardSelectVisible(false);
          setSelectedDashboard('');
          setChartName('');
        }}
        okText="确定"
        cancelText="取消"
      >
        <div style={{ marginBottom: 16 }}>
          <div style={{ marginBottom: 8, color: 'rgba(0, 0, 0, 0.85)' }}>选择仪表盘</div>
          <Select
            style={{ width: '100%' }}
            placeholder="请选择仪表盘"
            options={dashboards}
            value={selectedDashboard}
            onChange={(value) => setSelectedDashboard(value)}
          />
        </div>
        <div>
          <div style={{ marginBottom: 8, color: 'rgba(0, 0, 0, 0.85)' }}>图表名称</div>
          <Input placeholder="请输入图表名称" value={chartName} onChange={(e) => setChartName(e.target.value)} />
        </div>
      </Modal>
    </div>
  );
};

export default DataAnalysis;
